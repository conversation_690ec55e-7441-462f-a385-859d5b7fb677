<template>
  <div class="privacy_policy_wrap" :class="isMobileBool ? 'isMobile' : ''">
    <!-- 隐私政策内容 -->
      <!-- 复用 ChatHeader 组件样式，但只保留关闭功能 -->
      <section class="live_chat_header">
        <div class="header_wrap">
          <div class="header_cs_status">
            {{ $c("popupTitle") }}
          </div>
          <div class="header_right">
            <div class="icon_wrap close_icon_wrap" @click="closeChat">
              <span class="iconfont iconfont_close">&#xf30a;</span>
            </div>
          </div>
        </div>
      </section>

      <!-- 隐私政策内容区域 -->
      <div class="privacy_policy_content">
        <div class="privacy_policy_text">
          <template v-if="!isRejected">
            <div v-html="$c('privacyPolicy.acceptText')"></div>
          </template>
          <template v-else>
            {{ $c('privacyPolicy.rejectText') }}
          </template>
        </div>

        <!-- 操作按钮 -->
        <div class="privacy_policy_buttons" v-if="!isRejected">
          <FsButton
            type="blackline"
            @click="acceptPolicy"
            class="accept_btn"
          >
            {{ $c('privacyPolicy.acceptBtn') }}
          </FsButton>
          <FsButton
            type="blackline"
            @click="rejectPolicy"
            class="reject_btn"
          >
            {{ $c('privacyPolicy.rejectBtn') }}
          </FsButton>
        </div>
      </div>
  </div>
  <div class="mask" v-if="isMobileBool">
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import FsButton from '@/components/FsButton.vue';
import { $c } from '@/plugins/c-inject';
import { isMobile } from '@/util/util';

// Props
interface Props {
  isLoading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false
});

const emit = defineEmits(['accept-policy', 'reject-policy', 'close-chat']);

const isRejected = ref(false);

// 移动端检测
const isMobileBool = computed(() => isMobile());

const acceptPolicy = () => {
  emit('accept-policy');
};

const rejectPolicy = () => {
  isRejected.value = true;
  emit('reject-policy');
};

const closeChat = () => {
  emit('close-chat');
};
</script>

<style scoped lang="scss">
@import "../ChatHeader/index.scss";
.privacy_policy_wrap {
    position: fixed;
    width: calc(100vw - 32px);
    // height: calc(100vh - 32px);
    bottom: 16px;
    left: 16px;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0px 2px 14px 0px rgba(137, 137, 140, 0.15);
    overflow: hidden;
    z-index: 200;
    display: flex;
    flex-direction: column;
    &.isMobile{
      width: 100vw;
      bottom: 0;
      left: 0;
      border-bottom-left-radius:0 ;
      border-bottom-right-radius: 0;
    }
}
.mask {
  position: fixed;
  width: 100vh;
  height: 100vh;
  top: 0;
  left: 0;
  background-color: rgba(25, 25, 26, 0.6);;
}
// 隐私政策内容区域样式
.privacy_policy_content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 16px 16px 56px;
  text-align: center;
  background-color: #fff;
}

.privacy_policy_text {
  @include font14();
  color: #3D3D3D;
  margin-bottom: 56px;
}

.privacy_policy_buttons {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 100%;
  :deep(.fs-button) {
    flex: 1;
  }
}

</style>
